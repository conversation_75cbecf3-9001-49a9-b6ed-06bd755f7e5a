{"test_info": {"success": true, "execution_time": 40.918247, "test_method": "test_ParagAQUANT", "customer": "ParagTraders", "relearn_mode": false, "test_directory": "Test\\Customer\\ParagTraders\\TestResults\\Reference\\TestAQUANT\\test_ParagAQUANT\\AQUANT_d7aa65b5", "logs_directory": "Test\\Customer\\ParagTraders\\TestResults\\Reference\\TestAQUANT\\test_ParagAQUANT\\AQUANT_d7aa65b5\\Logs"}, "timestamp": "2025-07-24T23:38:52.793857", "directories": {"test_dir": "Test\\Customer\\ParagTraders\\TestResults\\Reference\\TestAQUANT\\test_ParagAQUANT\\AQUANT_d7aa65b5", "reference_dir": "Test\\Customer\\ParagTraders\\TestResults\\Input\\TestAQUANT\\test_ParagAQUANT\\AQUANT_d7aa65b5", "logs_dir": "Test\\Customer\\ParagTraders\\TestResults\\Reference\\TestAQUANT\\test_ParagAQUANT\\AQUANT_d7aa65b5\\Logs"}, "comparison_results": [{"files_compared": ["Test\\Customer\\ParagTraders\\TestResults\\Reference\\TestAQUANT\\test_ParagAQUANT\\AQUANT_d7aa65b5\\generated_request.xml", "Test\\Customer\\ParagTraders\\TestResults\\Input\\TestAQUANT\\test_ParagAQUANT\\AQUANT_d7aa65b5\\generated_request.xml"], "ignored_tags": ["VOUCHERNUMBER", "UDF:_UDF_805347380.LIST", "LASTVCHID"], "identical": true, "differences": [], "comparison_timestamp": "2025-07-24T23:38:52.790086", "relative_path": "generated_request.xml", "file_type": "XML Request"}, {"files_compared": ["Test\\Customer\\ParagTraders\\TestResults\\Reference\\TestAQUANT\\test_ParagAQUANT\\AQUANT_d7aa65b5\\Report_PV-WITH-INVENTORY_V1.csv", "Test\\Customer\\ParagTraders\\TestResults\\Input\\TestAQUANT\\test_ParagAQUANT\\AQUANT_d7aa65b5\\Report_PV-WITH-INVENTORY_V1.csv"], "ignored_columns": ["Received Date"], "identical": true, "differences": [], "comparison_timestamp": "2025-07-24T23:38:52.792656", "relative_path": "Report_PV-WITH-INVENTORY_V1.csv", "file_type": "CSV Report"}, {"files_compared": ["Test\\Customer\\ParagTraders\\TestResults\\Reference\\TestAQUANT\\test_ParagAQUANT\\AQUANT_d7aa65b5\\TallyImportedResponseXML\\generated_request.xml", "Test\\Customer\\ParagTraders\\TestResults\\Input\\TestAQUANT\\test_ParagAQUANT\\AQUANT_d7aa65b5\\TallyImportedResponseXML\\generated_request.xml"], "identical": false, "error": "Error comparing XML files: no element found: line 1, column 0", "comparison_timestamp": "2025-07-24T23:38:52.793672", "relative_path": "TallyImportedResponseXML\\generated_request.xml", "file_type": "XML Request"}], "summary": {"total_files": 3, "passed": 2, "failed": 1}}