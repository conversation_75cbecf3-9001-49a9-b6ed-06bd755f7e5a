{"test_info": {"success": true, "execution_time": 8.657044, "test_method": "test_ParagBSNL", "customer": "ParagTraders", "relearn_mode": false, "test_directory": "Test\\Customer\\ParagTraders\\TestResults\\Reference\\TestBSNL\\test_ParagBSNL\\BSNL_1a71bc9a", "logs_directory": "Test\\Customer\\ParagTraders\\TestResults\\Reference\\TestBSNL\\test_ParagBSNL\\BSNL_1a71bc9a\\Logs"}, "timestamp": "2025-07-24T23:53:08.750712", "directories": {"test_dir": "Test\\Customer\\ParagTraders\\TestResults\\Reference\\TestBSNL\\test_ParagBSNL\\BSNL_1a71bc9a", "reference_dir": "Test\\Customer\\ParagTraders\\TestResults\\Input\\TestBSNL\\test_ParagBSNL\\BSNL_1a71bc9a", "logs_dir": "Test\\Customer\\ParagTraders\\TestResults\\Reference\\TestBSNL\\test_ParagBSNL\\BSNL_1a71bc9a\\Logs"}, "comparison_results": [{"files_compared": ["Test\\Customer\\ParagTraders\\TestResults\\Reference\\TestBSNL\\test_ParagBSNL\\BSNL_1a71bc9a\\Report_PV-WITHOUT-INVENTORY_V1.csv", "Test\\Customer\\ParagTraders\\TestResults\\Input\\TestBSNL\\test_ParagBSNL\\BSNL_1a71bc9a\\Report_PV-WITHOUT-INVENTORY_V1.csv"], "ignored_columns": ["Received Date"], "identical": false, "differences": ["--- Report_PV-WITHOUT-INVENTORY_V1.csv\n", "+++ Report_PV-WITHOUT-INVENTORY_V1.csv\n", "@@ -1,2 +1,2 @@\n", " Sr. No.,Vendor Name,File Name,Unique Doc No,Document Date,Total Amount,Total Pages,Model Used,Document Format,Estimated Time Saved,AVTally Status,PriceList Verification,AccuVelocity Comments\n", "-1,UNK<PERSON><PERSON><PERSON>,BSNL.pdf,WMPR25001596979,04/05/2024,₹384,1,-,<PERSON><PERSON><PERSON>,-,Skipped,No,-", "+1,<PERSON><PERSON><PERSON><PERSON><PERSON>,BSNL.pdf,WMPR25001596979,04/05/2024,₹384,1,AstraLogicV3-<PERSON>,<PERSON>anne<PERSON>,~ 2 min 30 sec,Success,No,-"], "comparison_timestamp": "2025-07-24T23:53:08.747784", "relative_path": "Report_PV-WITHOUT-INVENTORY_V1.csv", "file_type": "CSV Report"}], "summary": {"total_files": 1, "passed": 0, "failed": 1}}