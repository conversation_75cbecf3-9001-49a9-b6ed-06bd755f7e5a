
<!DOCTYPE html>
<html>
<head>
    <title>AccuVelocity Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 10px; border-radius: 5px; }
        .summary { margin: 20px 0; }
        .passed { color: green; }
        .failed { color: red; }
        .file-comparison { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        .differences { background-color: #fff5f5; padding: 10px; margin: 10px 0; }
        pre { background-color: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>AccuVelocity Test Report</h1>
        <p><strong>Test:</strong> test_ParagHansgrohe</p>
        <p><strong>Customer:</strong> ParagTraders</p>
        <p><strong>Mode:</strong> Validation</p>
        <p><strong>Timestamp:</strong> 2025-07-24T21:38:34.701188</p>
        <p><strong>Execution Time:</strong> 5.83 seconds</p>
    </div>

    <div class="summary">
        <h2>Summary</h2>
        <p><strong>Total Files:</strong> 3</p>
        <p class="passed"><strong>Passed:</strong> 2</p>
        <p class="failed"><strong>Failed:</strong> 1</p>
    </div>

    <div class="details">
        <h2>File Comparison Details</h2>

        <div class="file-comparison">
            <h3 class="passed">✅ XML Request (generated_request.xml)</h3>
            <p><strong>Files:</strong> D:\Customer\Real\Accuvelocity\Code\Accuvelocity_exe\Test\Customer\ParagTraders\TestResults\Input\TestHansgrohe\test_ParagHansgrohe\Hansgrohe_Invoice_IN2410027104_b9a5ac59\generated_request.xml, D:\Customer\Real\Accuvelocity\Code\Accuvelocity_exe\Test\Customer\ParagTraders\TestResults\Reference\TestHansgrohe\test_ParagHansgrohe\Hansgrohe_Invoice_IN2410027104_b9a5ac59\generated_request.xml</p>
            <p><strong>Status:</strong> Identical</p>
</div>
        <div class="file-comparison">
            <h3 class="passed">✅ CSV Report (Report_PV-WITH-INVENTORY_V1.csv)</h3>
            <p><strong>Files:</strong> D:\Customer\Real\Accuvelocity\Code\Accuvelocity_exe\Test\Customer\ParagTraders\TestResults\Input\TestHansgrohe\test_ParagHansgrohe\Hansgrohe_Invoice_IN2410027104_b9a5ac59\Report_PV-WITH-INVENTORY_V1.csv, D:\Customer\Real\Accuvelocity\Code\Accuvelocity_exe\Test\Customer\ParagTraders\TestResults\Reference\TestHansgrohe\test_ParagHansgrohe\Hansgrohe_Invoice_IN2410027104_b9a5ac59\Report_PV-WITH-INVENTORY_V1.csv</p>
            <p><strong>Status:</strong> Identical</p>
</div>
        <div class="file-comparison">
            <h3 class="failed">❌ XML Request (TallyImportedResponseXML\generated_request.xml)</h3>
            <p><strong>Files:</strong> D:\Customer\Real\Accuvelocity\Code\Accuvelocity_exe\Test\Customer\ParagTraders\TestResults\Input\TestHansgrohe\test_ParagHansgrohe\Hansgrohe_Invoice_IN2410027104_b9a5ac59\TallyImportedResponseXML\generated_request.xml, D:\Customer\Real\Accuvelocity\Code\Accuvelocity_exe\Test\Customer\ParagTraders\TestResults\Reference\TestHansgrohe\test_ParagHansgrohe\Hansgrohe_Invoice_IN2410027104_b9a5ac59\TallyImportedResponseXML\generated_request.xml</p>
            <p><strong>Status:</strong> Different</p>
<p class='failed'><strong>Error:</strong> Error comparing XML files: prefix 'UDF' not found in prefix map</p></div>
    </div>
</body>
</html>
