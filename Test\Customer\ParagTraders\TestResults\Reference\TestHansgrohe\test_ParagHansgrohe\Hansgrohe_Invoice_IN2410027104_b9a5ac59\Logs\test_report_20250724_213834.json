{"test_info": {"success": true, "execution_time": 5.829833, "test_method": "test_ParagHansgrohe", "customer": "ParagTraders", "relearn_mode": false, "test_directory": "D:\\Customer\\Real\\Accuvelocity\\Code\\Accuvelocity_exe\\Test\\Customer\\ParagTraders\\TestResults\\Reference\\TestHansgrohe\\test_ParagHansgrohe\\Hansgrohe_Invoice_IN2410027104_b9a5ac59", "logs_directory": "D:\\Customer\\Real\\Accuvelocity\\Code\\Accuvelocity_exe\\Test\\Customer\\ParagTraders\\TestResults\\Reference\\TestHansgrohe\\test_ParagHansgrohe\\Hansgrohe_Invoice_IN2410027104_b9a5ac59\\Logs"}, "timestamp": "2025-07-24T21:38:34.701188", "directories": {"test_dir": "D:\\Customer\\Real\\Accuvelocity\\Code\\Accuvelocity_exe\\Test\\Customer\\ParagTraders\\TestResults\\Reference\\TestHansgrohe\\test_ParagHansgrohe\\Hansgrohe_Invoice_IN2410027104_b9a5ac59", "reference_dir": "D:\\Customer\\Real\\Accuvelocity\\Code\\Accuvelocity_exe\\Test\\Customer\\ParagTraders\\TestResults\\Reference\\TestHansgrohe\\test_ParagHansgrohe\\Hansgrohe_Invoice_IN2410027104_b9a5ac59", "logs_dir": "D:\\Customer\\Real\\Accuvelocity\\Code\\Accuvelocity_exe\\Test\\Customer\\ParagTraders\\TestResults\\Reference\\TestHansgrohe\\test_ParagHansgrohe\\Hansgrohe_Invoice_IN2410027104_b9a5ac59\\Logs"}, "comparison_results": [{"files_compared": ["D:\\Customer\\Real\\Accuvelocity\\Code\\Accuvelocity_exe\\Test\\Customer\\ParagTraders\\TestResults\\Input\\TestHansgrohe\\test_ParagHansgrohe\\Hansgrohe_Invoice_IN2410027104_b9a5ac59\\generated_request.xml", "D:\\Customer\\Real\\Accuvelocity\\Code\\Accuvelocity_exe\\Test\\Customer\\ParagTraders\\TestResults\\Reference\\TestHansgrohe\\test_ParagHansgrohe\\Hansgrohe_Invoice_IN2410027104_b9a5ac59\\generated_request.xml"], "ignored_tags": ["VOUCHERNUMBER", "UDF:_UDF_805347380.LIST"], "identical": true, "differences": [], "comparison_timestamp": "2025-07-24T21:38:34.697736", "file_type": "XML Request", "relative_path": "generated_request.xml"}, {"files_compared": ["D:\\Customer\\Real\\Accuvelocity\\Code\\Accuvelocity_exe\\Test\\Customer\\ParagTraders\\TestResults\\Input\\TestHansgrohe\\test_ParagHansgrohe\\Hansgrohe_Invoice_IN2410027104_b9a5ac59\\Report_PV-WITH-INVENTORY_V1.csv", "D:\\Customer\\Real\\Accuvelocity\\Code\\Accuvelocity_exe\\Test\\Customer\\ParagTraders\\TestResults\\Reference\\TestHansgrohe\\test_ParagHansgrohe\\Hansgrohe_Invoice_IN2410027104_b9a5ac59\\Report_PV-WITH-INVENTORY_V1.csv"], "ignored_columns": ["Received Date"], "identical": true, "differences": [], "comparison_timestamp": "2025-07-24T21:38:34.699078", "file_type": "CSV Report", "relative_path": "Report_PV-WITH-INVENTORY_V1.csv"}, {"files_compared": ["D:\\Customer\\Real\\Accuvelocity\\Code\\Accuvelocity_exe\\Test\\Customer\\ParagTraders\\TestResults\\Input\\TestHansgrohe\\test_ParagHansgrohe\\Hansgrohe_Invoice_IN2410027104_b9a5ac59\\TallyImportedResponseXML\\generated_request.xml", "D:\\Customer\\Real\\Accuvelocity\\Code\\Accuvelocity_exe\\Test\\Customer\\ParagTraders\\TestResults\\Reference\\TestHansgrohe\\test_ParagHansgrohe\\Hansgrohe_Invoice_IN2410027104_b9a5ac59\\TallyImportedResponseXML\\generated_request.xml"], "identical": false, "error": "Error comparing XML files: prefix 'UDF' not found in prefix map", "comparison_timestamp": "2025-07-24T21:38:34.700722", "file_type": "XML Request", "relative_path": "TallyImportedResponseXML\\generated_request.xml"}], "summary": {"total_files": 3, "passed": 2, "failed": 1}}