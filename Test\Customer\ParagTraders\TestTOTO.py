"""
TestTOTO.py - TOTO vendor test for ParagTraders

This file contains only the test method and global configuration.
All helper functions are in HelperModule.py

Directory Structure Created:
Input/Reference -> TestTOTO -> test_ParagTOTO -> filename_hash -> files + Logs
"""

import sys
import os
from pathlib import Path

# Add the Test directory to the path to import HelperModule
test_dir = str(Path(__file__).parent.parent.parent.absolute())  # Go up to Test directory
if test_dir not in sys.path:
    sys.path.insert(0, test_dir)
from HelperModule import run_test

# ================================
# GLOBAL CONFIGURATION - MODIFY THESE SETTINGS
# ================================

# Test Configuration
CUSTOMER_NAME = "ParagTraders"
TEST_FILE_NAME = "TestTOTO"  # This file name without .py extension
TEST_METHOD = "test_ParagTOTO"
RELEARN_MODE = True  # True = Learning mode, False = Validation mode

# File Paths (relative to this file's directory)
INPUT_FILE = "FilesForTesting/TOTO.pdf"
LICENSE_FILE = "Licenses/Developer_4_license_DevMode_True.lic"  # Direct license file path

# Processing Options
VOUCHER_TYPE = "purchase-with-inventory"  # Options: purchase-with-inventory, purchase-without-inventory, etc.

# Comparison Settings (for validation mode)
XML_IGNORE_TAGS = ["VOUCHERNUMBER", "UDF:_UDF_805347380.LIST", "LASTVCHID"]  # XML tags to ignore during comparison
CSV_IGNORE_COLUMNS = ["Received Date"]      # CSV columns to ignore during comparison

# ================================
# END OF GLOBAL CONFIGURATION
# ================================


def test_ParagTOTO():
    """Main test function for TOTO vendor"""
    return run_test(
        customer_name=CUSTOMER_NAME,
        test_file_name=TEST_FILE_NAME,
        test_method=TEST_METHOD,
        input_file=INPUT_FILE,
        license_file=LICENSE_FILE,
        voucher_type=VOUCHER_TYPE,
        relearn_mode=RELEARN_MODE,
        xml_ignore_tags=XML_IGNORE_TAGS,
        csv_ignore_columns=CSV_IGNORE_COLUMNS
    )


if __name__ == "__main__":
    print("🚀 Running TOTO Test for ParagTraders")
    print(f"📁 Test: {TEST_FILE_NAME}.{TEST_METHOD}")
    print(f"🔄 Learning mode: {RELEARN_MODE}")
    print("=" * 50)
    
    result = test_ParagTOTO()
    
    print("\n📊 Test Results:")
    print(f"  Success: {result.get('success', False)}")
    print(f"  Execution time: {result.get('execution_time', 0):.2f} seconds")
    
    if 'test_directory' in result:
        print(f"  Test directory: {result['test_directory']}")
    if 'logs_directory' in result:
        print(f"  Logs directory: {result['logs_directory']}")
    
    if result.get("success", False):
        print("✅ Test completed successfully!")
    else:
        print("❌ Test failed!")
        if "error" in result:
            print(f"Error: {result['error']}")
