"""
HelperModule.py - Core helper functions for AccuVelocity testing

This module contains reusable functions for executing AccuVelocity and comparing files.
No test logic or configuration here - only utility functions.
"""

import os
import sys
import json
import shutil
import hashlib
import subprocess
import zipfile
import xml.etree.ElementTree as ET
import csv
import difflib
from datetime import datetime


def get_file_path(base_dir, file_path):
    """Resolve file path: use absolute path if provided, else join with base_dir for relative path"""
    if os.path.isabs(file_path):
        return file_path
    return os.path.join(base_dir, file_path)


def get_file_hash(file_path):
    """Calculate MD5 hash of file for unique identification"""
    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()[:8]


def log_message(level, message, log_file=None):
    """Simple logging that works with or without CLogger"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_line = f"[{timestamp}] {level.upper()}: {message}"
    print(log_line)

    if log_file:
        try:
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(log_line + '\n')
        except Exception as e:
            print(f"Warning: Could not write to log file: {str(e)}")


def standardize_filename(filename):
    """Standardize filenames for consistent comparison"""
    if filename.endswith('.csv') and 'Report_' in filename:
        if 'PV-WITH-INVENTORY' in filename:
            return "Report_PV-WITH-INVENTORY_V1.csv"
        elif 'PV-WITHOUT-INVENTORY' in filename:
            return "Report_PV-WITHOUT-INVENTORY_V1.csv"
    elif filename.endswith('.xml') and 'REQ_' in filename:
        return "generated_request.xml"
    
    elif filename.endswith('.xml') and 'TallyXMLResponse' in filename:
        return "tally_response.xml"
    return filename


def extract_zip_files(zip_file, target_dir):
    """Extract files from zip with proper naming directly to target directory"""
    log_message("info", f"Extracting files from {zip_file} to {target_dir}")
    try:
        with zipfile.ZipFile(zip_file, 'r') as zip_ref:
            for file_name in zip_ref.namelist():
                if file_name.endswith('.xml') and file_name.startswith('REQ_'):
                    file_content = zip_ref.read(file_name)
                    dest_path = os.path.join(target_dir, "generated_request.xml")
                    with open(dest_path, 'wb') as f:
                        f.write(file_content)
                    log_message("info", "Extracted XML as: generated_request.xml")
                elif file_name.endswith('.xml') and file_name.startswith('TallyXMLResponse'):
                    file_content = zip_ref.read(file_name)
                    dest_path = os.path.join(target_dir, "tally_response.xml")
                    with open(dest_path, 'wb') as f:
                        f.write(file_content)
                    log_message("info", "Extracted XML as: tally_response.xml")
                else:
                    # Extract other files directly to target directory
                    dest_path = os.path.join(target_dir, standardize_filename(os.path.basename(file_name)))
                    with open(dest_path, 'wb') as f:
                        f.write(zip_ref.read(file_name))
                    log_message("info", f"Extracted: {file_name} as {os.path.basename(dest_path)}")
    except Exception as e:
        log_message("error", f"Error extracting zip: {str(e)}")
        raise


def copy_generated_files(source_dir, target_dir):
    """Copy all files and directories from source to target, standardizing filenames and preserving structure"""
    log_message("info", f"Copying files and directories from {source_dir} to {target_dir}")
    
    # Ensure target directory exists
    os.makedirs(target_dir, exist_ok=True)
    
    # Recursively copy all files and directories
    for root, dirs, files in os.walk(source_dir):
        # Compute relative path to preserve directory structure
        relative_path = os.path.relpath(root, source_dir)
        target_root = target_dir if relative_path == '.' else os.path.join(target_dir, relative_path)
        
        # Create corresponding directories in target
        os.makedirs(target_root, exist_ok=True)
        
        # Copy all files, including zip files
        for file_name in files:
            source_file = os.path.join(root, file_name)
            dest_name = standardize_filename(file_name)
            dest_file = os.path.join(target_root, dest_name)
            
            try:
                # Only copy if destination doesn't exist or filename was standardized
                if dest_name != file_name or not os.path.exists(dest_file):
                    shutil.copy2(source_file, dest_file)
                    log_message("info", f"Copied: {source_file} -> {dest_file}")
                else:
                    log_message("info", f"Skipped copying {source_file}: already exists as {dest_file}")
            except Exception as e:
                log_message("error", f"Failed to copy {source_file} to {dest_file}: {str(e)}")


def execute_accuvelocity(base_dir, input_file, license_file, voucher_type, test_dir, logs_dir, log_file=None):
    """Execute AccuVelocity with the given parameters, outputting directly to test_dir"""
    input_file_path = get_file_path(base_dir, input_file)
    license_file_path = get_file_path(base_dir, license_file)
    
    if not os.path.exists(input_file_path):
        raise FileNotFoundError(f"Input file not found: {input_file_path}")
    if not os.path.exists(license_file_path):
        raise FileNotFoundError(f"License file not found: {license_file_path}")

    log_message("info", f"Input file size: {os.path.getsize(input_file_path)} bytes")
    log_message("info", f"License file size: {os.path.getsize(license_file_path)} bytes")
    
    os.makedirs(test_dir, exist_ok=True)
    os.makedirs(logs_dir, exist_ok=True)
    
    log_message("info", f"Processing file: {input_file_path}", log_file)
    log_message("info", f"Using license: {license_file_path}", log_file)
    log_message("info", f"Output directory: {test_dir}", log_file)
    log_message("info", f"Logs directory: {logs_dir}", log_file)
    
    python_exe = os.path.join(os.path.dirname(sys.executable), "python.exe")
    main_script = os.path.join(base_dir, "Sourcecode", "Main.py")
    
    root_dir = base_dir  # Use base_dir directly, as it’s the working directory
    testing_env_path = os.path.join(root_dir, "testing.env")

    if os.path.exists(testing_env_path):
        log_message("info", f"Found testing.env at {testing_env_path}")
        command = [
            python_exe, main_script, input_file_path,
            "--dev-test-mode",
            "--dev-license-file", license_file_path,
            "--dev-request-dir", test_dir,  # Output directly to test_dir
            f"--{voucher_type}"
        ]
        working_dir = root_dir
    else:
        log_message("warning", f"testing.env not found at {testing_env_path}, using standard mode")
        command = [
            python_exe, main_script, input_file_path,
            f"--{voucher_type}"
        ]
        working_dir = os.path.dirname(main_script)

    log_message("info", f"Executing: {' '.join(command)}", log_file)
    log_message("info", f"Working directory: {working_dir}", log_file)

    start_time = datetime.now()
    result = subprocess.run(command, capture_output=True, text=True, cwd=working_dir)
    end_time = datetime.now()
    
    execution_time = (end_time - start_time).total_seconds()
    
    log_message("info", f"Execution completed in {execution_time:.2f} seconds", log_file)
    log_message("info", f"Return code: {result.returncode}", log_file)

    if result.stdout:
        log_message("info", f"STDOUT: {result.stdout}", log_file)
    if result.stderr:
        log_message("error", f"STDERR: {result.stderr}", log_file)

    if result.returncode != 0:
        log_message("error", f"AccuVelocity failed with return code {result.returncode}", log_file)
        log_message("error", f"Command was: {' '.join(command)}", log_file)
        log_message("error", f"Working directory: {working_dir}", log_file)
        return False, execution_time
    
    # Copy files from the output directory (where AccuVelocity writes) to test_dir
    output_dir = get_first_matching_output_dir(test_dir)
    if os.path.exists(output_dir):
        copy_generated_files(output_dir, test_dir)
        shutil.rmtree(output_dir, ignore_errors=True)
        log_message("info", f"Removed timestamped directory: {output_dir}")
    
    return True, execution_time


def get_first_matching_output_dir(base_dir: str, date_prefix: str = None) -> str:
    if date_prefix is None:
        date_prefix = datetime.now().strftime("%Y%m%d")

    matching_dirs = [
        name for name in os.listdir(base_dir)
        if os.path.isdir(os.path.join(base_dir, name)) and name.startswith(date_prefix)
    ]

    if not matching_dirs:
        raise FileNotFoundError(f"No folder in '{base_dir}' starts with '{date_prefix}'")

    # Return the first match (or sort if needed)
    matching_dirs.sort()  # Optional: for consistent ordering
    return os.path.join(base_dir, matching_dirs[0])


def compare_xml_files(file1, file2, ignore_tags=None):
    """Compare XML files ignoring specified tags and handling empty files."""
    if ignore_tags is None:
        ignore_tags = ["VOUCHERNUMBER", "UDF:_UDF_805347380.LIST"]

    try:
        size1 = os.path.getsize(file1)
        size2 = os.path.getsize(file2)

        if size1 == 0 and size2 == 0:
            return {
                "files_compared": [file1, file2],
                "ignored_tags": ignore_tags,
                "identical": True,
                "differences": [],
                "comparison_timestamp": datetime.now().isoformat()
            }
        elif size1 == 0 or size2 == 0:
            return {
                "files_compared": [file1, file2],
                "ignored_tags": ignore_tags,
                "identical": False,
                "differences": ["One file is empty and the other is not."],
                "comparison_timestamp": datetime.now().isoformat()
            }

        tree1 = ET.parse(file1)
        tree2 = ET.parse(file2)
        root1 = tree1.getroot()
        root2 = tree2.getroot()

        # Extract namespaces from file1
        ns_map = {}
        for event, elem in ET.iterparse(file1, events=['start-ns']):
            prefix, uri = elem
            ns_map[prefix] = uri

        # Function to handle tag clearing with or without namespace
        def clear_ignored_tags(root, tags, ns):
            for tag in tags:
                try:
                    if ":" in tag:
                        prefix, tag_name = tag.split(":", 1)
                        if prefix in ns:
                            xpath = f".//{{{ns[prefix]}}}{tag_name}"
                        else:
                            continue  # Skip unknown namespace
                    else:
                        xpath = f".//{tag}"
                    for elem in root.findall(xpath):
                        elem.clear()
                except Exception:
                    # If anything goes wrong, skip that tag
                    continue

        clear_ignored_tags(root1, ignore_tags, ns_map)
        clear_ignored_tags(root2, ignore_tags, ns_map)

        xml1_str = ET.tostring(root1, encoding="unicode")
        xml2_str = ET.tostring(root2, encoding="unicode")

        identical = xml1_str == xml2_str
        differences = []

        if not identical:
            differences = list(difflib.unified_diff(
                xml1_str.splitlines(keepends=True),
                xml2_str.splitlines(keepends=True),
                fromfile=os.path.basename(file1),
                tofile=os.path.basename(file2),
                n=3
            ))

        return {
            "files_compared": [file1, file2],
            "ignored_tags": ignore_tags,
            "identical": identical,
            "differences": differences,
            "comparison_timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        return {
            "files_compared": [file1, file2],
            "identical": False,
            "error": f"Error comparing XML files: {str(e)}",
            "comparison_timestamp": datetime.now().isoformat()
        }

def compare_csv_files(file1, file2, ignore_columns=None):
    """Compare CSV files ignoring specified columns"""
    if ignore_columns is None:
        ignore_columns = ["Received Date"]
    
    try:
        with open(file1, 'r', encoding='utf-8') as f1, \
             open(file2, 'r', encoding='utf-8') as f2:
            
            reader1 = csv.reader(f1)
            reader2 = csv.reader(f2)
            data1 = list(reader1)
            data2 = list(reader2)
        
        if data1 and data2 and ignore_columns:
            headers1 = data1[0]
            headers2 = data2[0]
            
            ignore_indices1 = [i for i, col in enumerate(headers1) if col in ignore_columns]
            ignore_indices2 = [i for i, col in enumerate(headers2) if col in ignore_columns]
            
            filtered_data1 = [[val for i, val in enumerate(row) if i not in ignore_indices1] for row in data1]
            filtered_data2 = [[val for i, val in enumerate(row) if i not in ignore_indices2] for row in data2]
        else:
            filtered_data1 = data1
            filtered_data2 = data2
        
        identical = filtered_data1 == filtered_data2
        differences = []
        
        if not identical:
            csv1_str = '\n'.join([','.join(row) for row in filtered_data1])
            csv2_str = '\n'.join([','.join(row) for row in filtered_data2])
            differences = list(difflib.unified_diff(
                csv1_str.splitlines(keepends=True),
                csv2_str.splitlines(keepends=True),
                fromfile=os.path.basename(file1),
                tofile=os.path.basename(file2),
                n=3
            ))
        
        return {
            "files_compared": [file1, file2],
            "ignored_columns": ignore_columns,
            "identical": identical,
            "differences": differences,
            "comparison_timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            "files_compared": [file1, file2],
            "identical": False,
            "error": f"Error comparing CSV files: {str(e)}",
            "comparison_timestamp": datetime.now().isoformat()
        }


if __name__ == "__main__":
    compare_xml_files(
        r"Test\Customer\ParagTraders\TestResults\Input\TestHansgrohe\test_ParagHansgrohe\Hansgrohe_Invoice_IN2410027104_b9a5ac59\TallyImportedResponseXML\generated_request.xml",
        r"Test\Customer\ParagTraders\TestResults\Reference\TestHansgrohe\test_ParagHansgrohe\Hansgrohe_Invoice_IN2410027104_b9a5ac59\TallyImportedResponseXML\generated_request.xml",
        ["VOUCHERNUMBER", "UDF:_UDF_805347380.LIST", "LASTVCHID"]
    )